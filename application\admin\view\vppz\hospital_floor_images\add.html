<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">医院:</label>
        <div class="col-xs-12 col-sm-8">
            {if condition="$hospital"}
                <input type="text" class="form-control" value="{$hospital.name}" readonly>
                <input type="hidden" name="row[hospital_id]" value="{$hospital.id}">
            {else/}
                <input id="c-hospital_id" data-rule="required" data-source="vppz/hospital/index" class="form-control selectpage" name="row[hospital_id]" type="text">
            {/if}
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">楼层标题:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-title" data-rule="required" class="form-control" name="row[title]" type="text">
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">楼层图片:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-pic" data-rule="required" class="form-control" size="50" name="row[pic]" type="text">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-pic" class="btn btn-danger faupload" data-input-id="c-pic" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-pic"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-pic" class="btn btn-primary fachoose" data-input-id="c-pic" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-pic"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-pic"></ul>
        </div>
    </div>

    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form> 