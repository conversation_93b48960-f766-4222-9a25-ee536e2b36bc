<?php

namespace app\admin\controller\vppz;

use app\common\controller\Backend;

/**
 * 
 *
 * @icon fa fa-circle-o
 */
class Order extends Base
{

    /**
     * Order模型对象
     * @var \app\admin\model\vppz\Order
     */
    protected $model = null;

	protected $searchFields = ['id','area.name','num','service_name','tel'];

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\vppz\Order;
        $this->view->assign("serviceStypeList", $this->model->getServiceStypeList());
        $this->view->assign("clientSexList", $this->model->getClientSexList());
        $this->view->assign("statusList", $this->model->getStatusList());
        $this->view->assign("refundStatusList", $this->model->getRefundStatusList());
        $this->view->assign("staffStatusList", $this->model->getStaffStatusList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

			$adminAreasWhere=$this->adminAreasWhere('area.id');

            $list = $this->model
                    ->with(['area','user','staff'])
                    ->where($where)->where($adminAreasWhere)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {

                $row->getRelation('area')->visible(['name']);
				$row->getRelation('user')->visible(['nickname','avatar']);
				$row->getRelation('staff')->visible(['nickname','avatar','sex','age','mobile','realname']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 手动退款
     */
    public function refund($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        // 检查订单状态：必须是已取消(40)且已付款的订单
        if ($row['status'] != 40) {
            $this->error('只能对已取消的订单进行退款');
        }

        // 检查是否已付款
        if ($row['pay'] <= 0) {
            $this->error('该订单未付款，无需退款');
        }

        // 检查退款状态：未退款(0)或退款失败(3),等待退款中(1),才能进行退款
        if (!in_array($row['refund_status'], [0,1,3])) {
            $this->error('该订单已在退款流程中或已退款成功');
        }

        if ($this->request->isPost()) {
            $refund_remark = $this->request->post('refund_remark', '管理员手动退款');

            // 使用数据库事务确保数据一致性
            \think\Db::startTrans();
            try {
                // 开始退款流程
                $refund_fee = $row['pay'];

                // 1. 更新订单退款状态为等待退款
                $orderUp = [
                    'refund_status' => 1,
                    'refund_money' => $refund_fee,
                    'refund_remark' => $refund_remark
                ];

                $ret = $this->model->save($orderUp, ['id' => $row['id'], 'refund_status' => $row['refund_status']]);
                if (!$ret) {
                    \think\Db::rollback();
                    $this->error('更新订单状态失败');
                }

                // 2. 调用微信退款接口
                $payConfig = \addons\epay\library\Service::getConfig('miniapp');

                // 临时调试：记录证书路径
                \think\Log::write('证书路径调试: cert_client = ' . ($payConfig['cert_client'] ?? 'undefined'));
                \think\Log::write('证书路径调试: cert_key = ' . ($payConfig['cert_key'] ?? 'undefined'));

                $refundRet = \Yansongda\Pay\Pay::wechat($payConfig)->refund([
                    'out_trade_no' => 'SEV_' . $row['id'],
                    'out_refund_no' => 'REFUND_' . $row['id'] . '_' . time(),
                    'total_fee' => $row['pay'] * 100,    // 退款单位是分
                    'refund_fee' => $refund_fee * 100,   // 退款单位是分
                    'refund_desc' => $refund_remark,
                    'type' => 'miniapp'
                ]);
                
                \think\Log::write('退款请求：' . json_encode($refundRet));

                // 3. 处理退款结果
                if (!$refundRet || $refundRet['return_code'] != 'SUCCESS' || $refundRet['result_code'] != 'SUCCESS') {
                    // 退款失败，更新状态
                    $this->model->save([
                        'refund_status' => 3,
                        'refund_tag' => serialize($refundRet),
                        'refund_time' => time()
                    ], ['id' => $row['id']]);

                    \think\Db::commit();
                    $error_msg = isset($refundRet['err_code_des']) ? $refundRet['err_code_des'] : '退款接口调用失败';
                    $this->error('退款失败：' . $error_msg);
                } else {
                    // 退款成功，更新状态
                    $this->model->save([
                        'refund_status' => 2,
                        'refund' => $refund_fee,
                        'refund_tag' => serialize($refundRet),
                        'refund_remark' => $refund_remark,
                        'refund_time' => time()
                    ], ['id' => $row['id']]);

                    \think\Db::commit();
                    $this->success('退款成功，资金将在24小时内原路退回用户账户');
                }

            } catch (\Exception $e) {
                \think\Db::rollback();
                \think\Log::write('退款异常：' . $e->getMessage());
                // 异常处理，将退款状态改为失败
                $this->model->save([
                    'refund_status' => 3,
                    'refund_tag' => $e->getMessage(),
                    'refund_time' => time()
                ], ['id' => $row['id']]);
                $this->error('退款异常：' . $e->getMessage());
            }
        }

        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

}
