<?php
namespace app\api\controller\vppz;

use app\admin\model\vppz\Station as StationModel;

/**
 * 小程序-服务站点相关接口
 */
class Station extends App
{
    // 所有接口无需登录
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    /**
     * 获取服务站点列表
     * @return void
     */
    public function list()
    {
        // 只返回启用的服务站点，可根据实际业务调整筛选条件
        $stations = StationModel::field('id,name,phone,address')
            ->order('id desc')
            ->select();
        $this->success('获取成功', $stations);
    }
} 