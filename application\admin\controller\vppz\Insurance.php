<?php
namespace app\admin\controller\vppz;

use app\admin\model\vppz\Insurance as InsuranceModel;

/**
 * 长护险管理
 */
class Insurance extends Base
{
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new InsuranceModel();
    }

    /**
     * 申请列表
     */
    public function index()
    {
        $this->relationSearch = true;
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $list = $this->model
                ->with(['client']) // 假如有 client 关联
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);

            foreach ($list as $row) {
                $row->getRelation('client')->visible(['name']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());
            return json($result);
        }
        // 分配状态列表到视图，供前端tab切换使用
        $this->view->assign("statusList", $this->model->getStatusList());
        return $this->view->fetch();
    }

    /**
     * 审批/详情
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $row->status = $params['status'];
                $row->status_remark = $params['status_remark'];
                $row->updatetime = time();
                $row->save();
                $this->success();
            }
            $this->error();
        }
        $this->view->assign("row", $row);
        $this->view->assign("statusList", $this->model->getStatusList());
        return $this->view->fetch();
    }

    /**
     * 审批
     */
    public function approve($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $row->status = $params['status'];
                $row->status_remark = $params['status_remark'];
                $row->updatetime = time();
                $row->save();
                $this->success('操作成功', '', null, true);
            }
            $this->error();
        }
        $this->view->assign("row", $row);
        $this->view->assign("statusList", $this->model->getStatusList());
        return $this->view->fetch('approve');
    }
} 