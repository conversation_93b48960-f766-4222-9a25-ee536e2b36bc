<?php

namespace app\admin\model\vppz;

use think\Model;
use traits\model\SoftDelete;

class Nav extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'vppz_nav';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'cat_text',
        'stype_text',
		'pic_image_url'	// 需在此处追加属性，才会触发获取器 getPicImageUrlAttr
    ];
    

    protected static function init()
    {
        self::afterInsert(function ($row) {
            $pk = $row->getPk();
            $row->getQuery()->where($pk, $row[$pk])->update(['weigh' => $row[$pk]]);
        });
    }

    
    public function getCatList()
    {
        return ['3' => __('Cat 3'), '4' => __('Cat 4')];
    }

    public function getStypeList()
    {
        return ['0' => __('Stype 0'), '1' => __('Stype 1'), '2' => __('Stype 2'), '3' => __('Stype 3'), '4' => __('Stype 4')];
    }


    public function getCatTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['cat']) ? $data['cat'] : '');
        $list = $this->getCatList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getStypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['stype']) ? $data['stype'] : '');
        $list = $this->getStypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }




    public function area()
    {
        return $this->belongsTo('Area', 'area_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }



	// 获取器直接转换url
	public function getPicImageUrlAttr($value,$data)
    {
        return \addons\vppz\library\Vpower::dourl($data['pic_image']);
    }
}
