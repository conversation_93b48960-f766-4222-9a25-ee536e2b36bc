<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">


    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">运营区名称:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text">
        </div>
    </div>

	<div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">覆盖范围:</label>
        <div class="col-xs-12 col-sm-8">
            <div class='control-relative'><input id="c-city" data-rule="required" class="form-control" data-toggle="city-picker" name="row[city]" type="text"></div>
			<h6 class="text-muted">如果不选择区县则表示覆盖到城市全区县</h6>
		</div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">接单模式:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-odmode" data-rule="required" class="form-control selectpicker" name="row[odmode]">
                {foreach name="odmodeList" item="vo"}
                    <option value="{$key}" {in name="key" value=""}selected{/in}>{$vo}</option>
                {/foreach}
            </select>
			<h6 class="text-muted">如果选择派单模式，需在陪护师中指定拥有派单权限的人员，该人员需关注新订单并及时派发给合适的陪护师</h6>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">收益分配:</label>
        <div class="col-xs-12 col-sm-8">
			<div class="input-group">
				<div class="input-group-addon">陪护师</div>
				<input id="c-profit" data-rule="required;range(0~100)" class="form-control" name="row[profit]" type="number">
				<div class="input-group-addon">推广者</div>
				<input id="c-tax_seller" data-rule="required;range(0~100)" class="form-control" name="row[tax_seller]" type="number">
				<div class="input-group-addon">团队长</div>
				<input id="c-tax_master" data-rule="required;range(0~100)" class="form-control" name="row[tax_master]" type="number">
				<div class="input-group-addon">总部</div>
				<input id="c-tax_plat" data-rule="required;range(0~100);taxsum" class="form-control" name="row[tax_plat]" type="number">
			</div>
            <h6 class="text-muted">以上数据皆为百分比，例：填80，则表示80%，上方所有比例相加不能超过100<br/>城市合伙人收益率为：100-上方所有比例之和<!--<br/>推广者奖励率功能目前未开通，该项为预留，未来可能使用或取消--></h6>
        </div>
    </div>
	<!--
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Tax_plat')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-tax_plat" data-rule="required" class="form-control" name="row[tax_plat]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Tax_master')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-tax_master" data-rule="required" class="form-control" name="row[tax_master]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Tax_seller')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-tax_seller" data-rule="required" class="form-control" name="row[tax_seller]" type="number">
        </div>
    </div>
	--->

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">推广者开通:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-seller_reg" data-rule="required" class="form-control selectpicker" name="row[seller_reg]">
                {foreach name="sellerRegList" item="vo"}
                    <option value="{$key}" {in name="key" value=""}selected{/in}>{$vo}</option>
                {/foreach}
            </select>
			<h6 class="text-muted">关闭开通不会影响已成为推广者的用户</h6>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">推广绑定时效:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-seller_bind" data-rule="required;integer" class="form-control" name="row[seller_bind]" type="number" step="1" >
			<h6 class="text-muted">推广关系绑定时效，单位“天”，超过时效则佣金不再计入。填0表示永久有效</h6>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">陪护师注册:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-staff_reg" data-rule="required" class="form-control selectpicker" name="row[staff_reg]">
                {foreach name="staffRegList" item="vo"}
                    <option value="{$key}" {in name="key" value=""}selected{/in}>{$vo}</option>
                {/foreach}
            </select>
			<h6 class="text-muted">如果开启邀请，则需同时开启陪护团队功能，在团队功能页面中获得邀请码</h6>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">陪护团队:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-staff_team" data-rule="required" class="form-control selectpicker" name="row[staff_team]">
                {foreach name="staffTeamList" item="vo"}
                    <option value="{$key}" {in name="key" value=""}selected{/in}>{$vo}</option>
                {/foreach}
            </select>
			<h6 class="text-muted">开启后，陪护师可通过邀请建立自己的团队，进而获得团队长奖励</h6>
        </div>
    </div>
    <div class="form-group" data-favisible="staff_team=2">
        <label class="control-label col-xs-12 col-sm-2">陪护团队说明:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-staff_team_intro" data-rule="required" class="form-control " rows="5" name="row[staff_team_intro]" cols="50"></textarea>
        </div>
    </div>

	<div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">陪护师名片:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-staff_card" data-rule="required" class="form-control selectpicker" name="row[staff_card]">
                {foreach name="staffCardList" item="vo"}
                    <option value="{$key}" {in name="key" value=""}selected{/in}>{$vo}</option>
                {/foreach}
            </select>
			<h6 class="text-muted">开启后，用户默认绑定邀请和上一次服务的陪护师</h6>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">客服电话:</label>
        <div class="col-xs-12 col-sm-8">
            
            <dl class="fieldlist" data-name="row[tels_json]">
                <dd>
                    <ins>客服名称</ins>
                    <ins>电话号码</ins>
                </dd>
                <dd><a href="javascript:;" class="btn btn-sm btn-success btn-append"><i class="fa fa-plus"></i> 添加客服</a></dd>
                <textarea name="row[tels_json]" class="form-control hide" cols="30" rows="5"></textarea>
            </dl>


        </div>
    </div>
	<!-- 有电话无需客服二维码
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Weixin_image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-weixin_image" data-rule="required" class="form-control" size="50" name="row[weixin_image]" type="text">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-weixin_image" class="btn btn-danger faupload" data-input-id="c-weixin_image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-weixin_image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-weixin_image" class="btn btn-primary fachoose" data-input-id="c-weixin_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-weixin_image"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-weixin_image"></ul>
        </div>
    </div>
	-->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">是否启用:</label>
		<div class="col-xs-12 col-sm-8">
			<input  id="c-use_switch" name="row[use_switch]" type="hidden" value="1">
			<a href="javascript:;" data-toggle="switcher" class="btn-switcher" data-input-id="c-use_switch"  data-yes="1" data-no="0">
			<i class="fa fa-toggle-on text-success  fa-2x"></i>
			</a>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">备注:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-remark" class="form-control " rows="5" name="row[remark]" cols="50"></textarea>
        </div>
    </div>
    
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
