<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    <!-- 申请人信息 -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">申请人：</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-client_name" data-rule="required" class="form-control" name="row[client_name]" type="text" value="{$row.client_name|default=''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">联系电话：</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-client_mobile" data-rule="required" class="form-control" name="row[client_mobile]" type="text" value="{$row.client_mobile|default=''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">身份证号：</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-client_idcard" class="form-control" name="row[client_idcard]" type="text" value="{$row.client_idcard|default=''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">性别：</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-client_sex" class="form-control" name="row[client_sex]">
                <option value="1" {eq name="row.client_sex" value="1"}selected{/eq}>男</option>
                <option value="2" {eq name="row.client_sex" value="2"}selected{/eq}>女</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">年龄：</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-client_age" class="form-control" name="row[client_age]" type="number" value="{$row.client_age|default=''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">地址：</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-address" class="form-control" name="row[address]" type="text" value="{$row.address|default=''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">备注：</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-remark" class="form-control" name="row[remark]" rows="3">{$row.remark|default=''}</textarea>
        </div>
    </div>
    <!-- 审批状态选择 -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">审批状态：</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
                {foreach name="statusList" item="vo" key="key"}
                <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label>
                {/foreach}
            </div>
        </div>
    </div>
    <!-- 审批备注 -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">审批备注：</label>
        <div class="col-xs-12 col-sm-8">
            <textarea class="form-control" name="row[status_remark]" rows="3">{$row.status_remark|default=''}</textarea>
        </div>
    </div>
    <!-- 申请证明图片 -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">申请证明图片：</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-proof_images" class="form-control" size="50" name="row[proof_images]" type="text" value="{$row.proof_images|default=''}">
                <div class="input-group-addon no-border no-padding">
                    <span>
                        <button type="button" id="faupload-proof_images" class="btn btn-danger faupload" data-input-id="c-proof_images" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="true" data-preview-id="p-proof_images">
                            <i class="fa fa-upload"></i> 上传
                        </button>
                    </span>
                    <span>
                        <button type="button" id="fachoose-proof_images" class="btn btn-primary fachoose" data-input-id="c-proof_images" data-mimetype="image/*" data-multiple="true">
                            <i class="fa fa-list"></i> 选择
                        </button>
                    </span>
                </div>
                <span class="msg-box n-right" for="c-proof_images"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-proof_images"></ul>
        </div>
    </div>
    <!-- 提交按钮 -->
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success">保存</button>
            <button type="reset" class="btn btn-default">重置</button>
        </div>
    </div>
</form>