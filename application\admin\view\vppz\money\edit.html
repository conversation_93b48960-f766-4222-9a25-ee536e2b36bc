<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('App_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-app_id" data-rule="required" data-source="app/index" class="form-control selectpage" name="row[app_id]" type="text" value="{$row.app_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Who')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-who" data-rule="required" class="form-control selectpicker" name="row[who]">
                {foreach name="whoList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.who"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Who_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-who_id" data-rule="required" data-source="who/index" class="form-control selectpage" name="row[who_id]" type="text" value="{$row.who_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[user_id]" type="text" value="{$row.user_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Money')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-money" data-rule="required" class="form-control" step="0.01" name="row[money]" type="number" value="{$row.money|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Biz')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-biz" data-rule="required" class="form-control selectpicker" name="row[biz]">
                {foreach name="bizList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.biz"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Biz_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-biz_id" data-rule="required" data-source="biz/index" class="form-control selectpage" name="row[biz_id]" type="text" value="{$row.biz_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Biz_type')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-biz_type" data-rule="required" class="form-control selectpicker" name="row[biz_type]">
                {foreach name="bizTypeList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.biz_type"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Biz_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-biz_name" data-rule="required" class="form-control" name="row[biz_name]" type="text" value="{$row.biz_name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Remark')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-remark" data-rule="required" class="form-control " rows="5" name="row[remark]" cols="50">{$row.remark|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
