define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'vppz/order/index' + location.search,
                    //add_url: 'vppz/order/add',
                    //edit_url: 'vppz/order/edit',
                    //del_url: 'vppz/order/del',
                    multi_url: 'vppz/order/multi',
                    import_url: 'vppz/order/import',
                    refund_url: 'vppz/order/refund',
                    table: 'vppz_order',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                //fixedColumns: true,
                //fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        //{field: 'app_id', title: __('App_id')},
                        {field: 'area.id', title:'所属运营区', operate: 'LIKE',addClass:'selectpage',extend:'data-source="vppz/area/index"',visible: false},
						{field: 'area.name', title:'所属运营区', operate: 'LIKE',searchable:false},

						{field: 'num', title: '订单号', operate: 'LIKE'},
						{field: 'title', title: '订单标题', operate: 'LIKE',visible: false},
                        //{field: 'service_id', title: __('Service_id')},
						{field: 'service_logo_image', title: '服务', operate: false, events: Table.api.events.image, formatter: Table.api.formatter.image},
						{field: 'service_name', title: '服务名称', operate: 'LIKE'},
                        {field: 'service_stype', title: __('Service_stype'), searchList: {"10":__('Service_stype 10'),"15":__('Service_stype 15'),"20":__('Service_stype 20'),"30":__('Service_stype 30'),"40":__('Service_stype 40'),"110":__('Service_stype 110'),"210":__('Service_stype 210')}, formatter: Table.api.formatter.normal,visible: false},
                        //{field: 'hospital_id', title: __('Hospital_id')},
                        
                        
						{field: 'user.avatar', title: '用户', operate: 'LIKE', events: Table.api.events.image, formatter: Table.api.formatter.image,searchable:false},
						{field: 'user.nickname', title: '用户昵称', operate: 'LIKE'},
						{field: 'tel', title: '用户电话', operate: 'LIKE'},

                        {field: 'openid', title: '用户openid', operate: 'LIKE',visible: false},

                        //{field: 'user_id', title: __('User_id')},

                        {field: 'price', title: __('Price'), operate:'BETWEEN',visible: false,searchable:false},
                        {field: 'cnt', title: __('Cnt'),visible: false,searchable:false},
                        {field: 'amount', title: __('Amount'), operate:'BETWEEN'},
                        //{field: 'fee', title: __('Fee'), operate:'BETWEEN',visible: false},
                        {field: 'cut', title: __('Cut'), operate:'BETWEEN',visible: false,searchable:false},
                        {field: 'to_pay', title: '需付', operate:'BETWEEN'},

						{field: 'pay', title: '已付', operate:'BETWEEN'},
						{field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime,visible: false},
						{field: 'pay_time', title: __('Pay_time'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},

						{field: 'status', title: __('Status'), searchList: {"10":__('Status 10'),"20":__('Status 20'),"30":__('Status 30'),"40":__('Status 40')}, formatter: Table.api.formatter.status,custom:{'10':'warning','20':'info','30':'success','40':'muted'}},//"0":__('Status 0'),
                        {field: 'status_time', title: '状态变更时间', operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime,visible: false},
                        //{field: 'pay_way', title: __('Pay_way')},
                        

						{field: 'staff_toid', title: '被派单者ID',visible: false,searchable:false},
                        //{field: 'staff_touid', title: __('Staff_touid')},
                        {field: 'staff_status', title: __('Staff_status'), searchList: {"0":__('Staff_status 0'),"10":__('Staff_status 10')}, formatter: Table.api.formatter.status,custom:{'0':'warning','10':'success'}},
                        {field: 'staff_id', title: '接单者ID',visible: false},
                        {field: 'staff_uid', title: '接单者UID',visible: false},
                       
						{field: 'staff.avatar', title: '接单者', operate: 'LIKE', events: Table.api.events.image, formatter: Table.api.formatter.image,searchable:false},
                        {field: 'staff.nickname', title: '接单者称呼', operate: 'LIKE'},
                        {field: 'staff.sex', title: '接单者性别',visible: false},
                        {field: 'staff.age', title: '接单者年龄',visible: false},
                        {field: 'staff.mobile', title: '接单者电话', operate: 'LIKE'},
                        {field: 'staff.realname', title: '接单者姓名', operate: 'LIKE',visible: false},
						{field: 'staff_time', title: '接单时间', operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime,visible: false},

                        {field: 'profit_fee', title: '接单收益', operate:'BETWEEN'},
						{field: 'profit', title: '接单收益率', operate:'BETWEEN',visible: false},
						
						{field: 'tax_master_fee', title: '团长收益', operate:'BETWEEN'},
						{field: 'tax_master', title: '团长收益率', operate:'BETWEEN',visible: false},
                        
						{field: 'tax_plat_fee', title: '总部收益', operate:'BETWEEN'},
                        {field: 'tax_plat', title: '总部收益率', operate:'BETWEEN',visible: false},
						
						{field: 'tax_seller_fee', title: '推广者收益', operate:'BETWEEN'},
                        {field: 'tax_seller', title: '推广者收益率', operate:'BETWEEN',visible: false},
                        
                        {field: 'tax_area_fee', title: '运营区收益', operate:'BETWEEN'},
        
                        //{field: 'client_id', title: __('Client_id')},
						{field: 'hospital_name', title: '医院', operate: 'LIKE'},
                        {field: 'client_name', title: '客户姓名', operate: 'LIKE',visible: false},
                        {field: 'client_sex', title: '客户性别', searchList: {"1":__('Client_sex 1'),"2":__('Client_sex 2')}, formatter: Table.api.formatter.normal,visible: false},
                        {field: 'client_age', title: '客户年龄',visible: false,searchable:false},
                        {field: 'client_mobile', title: '客户电话', operate: 'LIKE'},
                        {field: 'client_idcard', title: '客户身份证号', operate: 'LIKE',visible: false},
						
						//{field: 'address', title: '客户地址', operate: 'LIKE'},

                        {field: 'starttime', title: '服务开始时间', operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'endtime', title: '服务结束时间', operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},


                        //{field: 'pay_callback', title: __('Pay_callback'), operate: 'LIKE'},
                        {field: 'refund_status', title: __('Refund_status'), searchList: {"0":__('Refund_status 0'),"1":__('Refund_status 1'),"2":__('Refund_status 2'),"3":__('Refund_status 3')}, formatter: Table.api.formatter.status,custom:{'0':'muted','1':'warning','2':'success','3':'danger'}},
                        {field: 'refund_money', title: '退款金额', operate:'BETWEEN'},
                        {field: 'refund', title: '已退金额', operate:'BETWEEN'},
                        {field: 'refund_time', title: __('Refund_time'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},


                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate,
                         buttons: [
                             {
                                 name: 'refund',
                                 title: '退款',
                                 classname: 'btn btn-xs btn-warning btn-dialog',
                                 icon: 'fa fa-money',
                                 url: 'vppz/order/refund',
                                 visible: function(row) {
                                     // 只有已取消(40)且已付款且未成功退款的订单才显示退款按钮
                                     return row.status == 40 && row.pay > 0 && (row.refund_status == 0 || row.refund_status == 3 || row.refund_status == 1);
                                 },
                                 confirm: '确定要对此订单进行退款操作吗？退款后资金将原路退回用户账户。',
                                 extend: 'data-area-id="' + (typeof row !== 'undefined' && row.area_id ? row.area_id : '') + '"'
                             }
                         ],
                         formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        refund: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
