define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'vppz/money/index' + location.search,
                    add_url: 'vppz/money/add',
                    edit_url: 'vppz/money/edit',
                    del_url: 'vppz/money/del',
                    multi_url: 'vppz/money/multi',
                    import_url: 'vppz/money/import',
                    table: 'vppz_money',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                //fixedColumns: true,
                //fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'who', title: '对象类型', searchList: {"staff":__('Who staff'),"plat":__('Who plat'),"area":__('Who area'),"user":__('Who user'),"seller":__('Who seller')}, formatter: Table.api.formatter.normal},
                        {field: 'who_id', title: '对象ID',visible: false},
						{field: 'who_name', title: '对象名称', operate: 'LIKE'},
                        {field: 'user_id', title: '用户ID',visible: false,searchable:false},
						{field: 'user.avatar', title: '头像', operate: 'LIKE', events: Table.api.events.image, formatter: Table.api.formatter.image,visible: false,searchable:false},
                        {field: 'user.nickname', title: '昵称', operate: 'LIKE',visible: false,searchable:false},
                        {field: 'money', title: '收支', operate:'BETWEEN'},
                        {field: 'biz', title: '业务', searchList: {"order":__('Biz order'),"outcash":__('Biz outcash'),"settle":__('Biz settle')}, formatter: Table.api.formatter.normal},
                        {field: 'biz_id', title: '业务ID',visible: false,searchable:false},
                        {field: 'biz_type', title: '收益来源', searchList: {"service":__('Biz_type service'),"master":__('Biz_type master'),"plat":__('Biz_type plat'),"area":__('Biz_type area'),"seller":__('Biz_type seller')}, formatter: Table.api.formatter.normal,visible: false},
                        {field: 'biz_name', title: '详细', operate: 'LIKE'},
                        {field: 'createtime', title: '发生时间', operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
						
                       
                        //{field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
