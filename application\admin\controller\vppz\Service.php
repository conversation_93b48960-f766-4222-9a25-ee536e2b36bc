<?php

namespace app\admin\controller\vppz;


/**
 * 
 *
 * @icon fa fa-circle-o
 */
class Service extends Base
{

    /**
     * Service模型对象
     * @var \app\admin\model\vppz\Service
     */
    protected $model = null;

	protected $searchFields = ['id','name','tags'];

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\vppz\Service;
        $this->view->assign("stypeList", $this->model->getStypeList());

		// 给数据附加当前appid，appid由Base负责解析
		$this->model::event('before_insert', function ($row){
			$row->app_id=$this->app_id;
		});
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


}
