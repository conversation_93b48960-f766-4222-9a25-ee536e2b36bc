<?php
namespace app\api\controller\vppz;

use think\Db;
use app\admin\model\vppz\Insurance as InsuranceApplyModel;

class Insurance extends App
{
    /**
     * 新增长护险申请
     */
    public function add()
    {
        $mine = $this->_mine; // 当前登录用户
        $area = $this->_area;

        // 获取前端提交数据
        $form = input('form');
        if (empty($form)) {
            $this->error("内容填写有误，请重试");
        }
        $form = json_decode(urldecode($form), true);
        if (!$form) {
            $this->error("内容填写有误，请重试");
        }

        // 参数校验
        if (empty($form['client_id'])) {
            $this->error("请选择申请客户");
        }
        if (empty($form['proof_images'])) {
            $this->error("请上传申请证明图片");
        }

        // 组装数据
        $data = [
            'app_id'        => $this->app_id,
            'user_id'       => $mine['id'],
            'client_id'     => $form['client_id'],
            'client_name'   => $form['client_name'],
            'client_sex'    => $form['client_sex'],
            'client_age'    => $form['client_age'],
            'client_mobile' => $form['client_mobile'],
            'client_idcard' => $form['client_idcard'],
            'proof_images'  => $form['proof_images'],
            'remark'        => $form['remark'] ?? '',
            'status'        => 0,
            'createtime'    => time(),
            'updatetime'    => time()
        ];

        // 保存到数据库
        $model = new InsuranceApplyModel();
        $ret = $model->save($data);
        if (!$ret) {
            $this->error("保存失败，请重试");
        }
        $this->success("提交成功，等待审核");
    }

    /**
     * 获取当前用户的最新申请信息
     */
    public function mine()
    {
        $mine = $this->_mine; // 当前登录用户
        $row = \app\admin\model\vppz\Insurance::where('user_id', $mine['id'])
            ->order('createtime', 'desc')
            ->find();

        if (!$row) {
            $this->error('暂无申请记录');
        }

        // 处理图片为数组
        $proofImages = [];
        if (!empty($row['proof_images'])) {
            $proofImages = explode(',', $row['proof_images']);
        }

        $data = [
            'applyDate' => date('Y-m-d', $row['createtime']),
            'userName' => $row['client_name'],
            'proofImages' => $proofImages,
            'remark' => $row['remark']
        ];
        $this->success('ok', $data);
    }
} 