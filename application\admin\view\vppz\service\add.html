<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">



    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">服务类型:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-stype" data-rule="required" class="form-control selectpicker" name="row[stype]">
				<option value="">请选择...</option>
                {foreach name="stypeList" item="vo"}
                    <option value="{$key}" {in name="key" value=""}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">服务名称:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text">
        </div>
    </div>

	<div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">唯一标识:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-code" data-rule="required" class="form-control" name="row[code]" type="text">
			<h6 class="text-muted">用于唯一标识服务，只能用英文，比如：“peizhen”，设定后不能更改</h6>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">展示图片:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-logo_image" data-rule="required" class="form-control" size="50" name="row[logo_image]" type="text" placeholder="建议尺寸：500x500px">
				
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-logo_image" class="btn btn-danger faupload" data-input-id="c-logo_image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-logo_image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-logo_image" class="btn btn-primary fachoose" data-input-id="c-logo_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-logo_image"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-logo_image"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">服务图标:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-icon_image" data-rule="required" class="form-control" size="50" name="row[icon_image]" type="text" placeholder="建议尺寸：100x100px">
				
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-icon_image" class="btn btn-danger faupload" data-input-id="c-icon_image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-icon_image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-icon_image" class="btn btn-primary fachoose" data-input-id="c-icon_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-icon_image"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-icon_image"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">分组标签</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-tags" data-rule="required" class="form-control" data-role="tagsinput" name="row[tags]" type="text">
			<h6 class="text-muted" style="margin-top:0;">通过将服务分组，可在导航中跳转到某个分组页面显示同一分组下的服务集合，服务可参与到多个分组，多个分组用半角逗号分隔。导航页面路径填写格式：/vp_pz/pages/index/tag?tag=分组名称</h6>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">一句话简介:</label>
        <div class="col-xs-12 col-sm-8">
			 <input id="c-intro" data-rule="required" class="form-control"  name="row[intro]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">服务说明:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-info" data-rule="required" class="form-control " rows="5" name="row[info]" cols="50"></textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">服务详情:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-content" class="form-control editor" rows="5" name="row[content]" cols="50"></textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">服务价格:</label>
        <div class="col-xs-12 col-sm-8">

			<div class="input-group">
				<div class="input-group-addon">当前价格（元）</div>
				<input id="c-price" data-rule="required;range(0.01~)" class="form-control" step="0.01" name="row[price]" type="number">
				<div class="input-group-addon">划线价格（元）</div>
				<input id="c-priceo" data-rule="required;range(0.01~);priceo" class="form-control" step="0.01" name="row[priceo]" type="number">
			</div>
           
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">是否上架:</label>
        <div class="col-xs-12 col-sm-8">
			<input  id="c-use_switch" name="row[use_switch]" type="hidden" value="1">
			<a href="javascript:;" data-toggle="switcher" class="btn-switcher" data-input-id="c-use_switch"  data-yes="1" data-no="0">
			<i class="fa fa-toggle-on text-success  fa-2x"></i>
			</a>
        </div>
    </div>

    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
