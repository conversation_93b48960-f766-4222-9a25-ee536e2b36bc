<?php

namespace app\admin\controller\vppz;

use app\common\controller\Backend;

use think\Db;

/**
 * 用户提现记录管理
 *
 * @icon fa fa-circle-o
 */
class Outcash extends Base
{

    /**
     * Outcash模型对象
     * @var \app\admin\model\vppz\Outcash
     */
    protected $model = null;

	protected $searchFields = ['id','area.name','nickname','money','channel_account','remark'];

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\vppz\Outcash;
        $this->view->assign("bizList", $this->model->getBizList());
        $this->view->assign("statusList", $this->model->getStatusList());
        $this->view->assign("channelList", $this->model->getChannelList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

			$adminAreasWhere=$this->adminAreasWhere('area_id');

            $list = $this->model
                    ->with(['area','admin'])
                    ->where($where)->where($adminAreasWhere)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                
                $row->getRelation('area')->visible(['name']);
				$row->getRelation('admin')->visible(['username','nickname','avatar']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }


	
    /**
     * 处理
     */
    public function deal()
    {	
		$ids = input('ids');
        $outcash = $this->model->get(['id' => $ids]);
        if (!$outcash) {
            $this->error(__('No Results were found'));
        }
        
		if ($this->request->isPost()) {
			$row = input('row/a');
			
			if($outcash['status']==0){
				// 处理
				$row['admin_id']=$this->auth->id;
				$row['status_time']=time();
				$this->model->save($row,['id' =>$outcash['id']]);

				// 是否需要退账
				if($row['status']==3){
					if('staff'==$outcash['biz']){
						// 账户回款
						$ret = Db::name('vppz_staff')->where(['app_id'=>$this->app_id,'id'=>$outcash['biz_id']])->inc('money',$outcash['money'])->dec('outcash',$outcash['money'])->update();
						if(!($ret>0)){
							$this->error("操作失败，请重试");
						}

						$this->recordMoney([
							'who'=>'staff',
							'who_id'=>$outcash['biz_id'],
							'who_name'=>$outcash['nickname'],
							'user_id'=>$outcash['user_id'],
							'money'=> $outcash['money'],
							'biz'=>'outcash',
							'biz_id'=>$outcash['id'],
							'biz_type'=>'outcash',
							'biz_name'=>'提现失败退账'.$outcash['money'].'元'
						]);
					}else if('seller'==$outcash['biz']){
						// 账户回款
						$ret = Db::name('vppz_user')->where(['app_id'=>$this->app_id,'id'=>$outcash['biz_id']])->inc('sell_money',$outcash['money'])->dec('sell_outcash',$outcash['money'])->update();
						if(!($ret>0)){
							$this->error("操作失败，请重试");
						}

						$this->recordMoney([
							'who'=>'seller',
							'who_id'=>$outcash['biz_id'],
							'who_name'=>$outcash['nickname'],
							'user_id'=>$outcash['user_id'],
							'money'=> $outcash['money'],
							'biz'=>'outcash',
							'biz_id'=>$outcash['id'],
							'biz_type'=>'outcash',
							'biz_name'=>'提现失败退账'.$outcash['money'].'元'
						]);
					}
				}

				$this->success("处理成功");
			}else{
				// 保存更新备注
				$this->model->save($row,['id' =>$outcash['id']]);
				$this->success("保存成功");
			}
		}else{
			
			$this->view->assign("row", $outcash);
			return $this->view->fetch();
		}
    }

}
