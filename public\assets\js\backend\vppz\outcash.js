define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'vppz/outcash/index' + location.search,
                    //add_url: 'vppz/outcash/add',
                    //edit_url: 'vppz/outcash/edit',
                    //del_url: 'vppz/outcash/del',
                    multi_url: 'vppz/outcash/multi',
                    import_url: 'vppz/outcash/import',
                    table: 'vppz_outcash',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'area.id', title:'所属运营区', operate: 'LIKE',addClass:'selectpage',extend:'data-source="vppz/area/index"',visible: false},
						{field: 'area.name', title:'所属运营区', operate: 'LIKE',searchable:false},
                        {field: 'biz', title: '提现者角色', searchList: {"staff":__('Biz staff'),"user":__('Biz user'),"seller":__('Biz seller'),"area":__('Biz area')}, formatter: Table.api.formatter.normal},
                        {field: 'biz_id', title: __('Biz_id'),visible: false,searchable:false},
                        {field: 'user_id', title: __('User_id'),visible: false,searchable:false},
                        {field: 'nickname', title: '提现用户', operate: 'LIKE'},
                        //{field: 'weixin', title: __('Weixin'), operate: 'LIKE'},
                        {field: 'mobile', title: '联系电话', operate: 'LIKE'},
                        {field: 'openid', title: __('Openid'), operate: 'LIKE',visible: false,searchable:false},
						{field: 'money', title: '提现金额', operate:'BETWEEN'},
                        {field: 'cash', title: '实际金额', operate:'BETWEEN'},
                        {field: 'money_before', title:'提前余额', operate:'BETWEEN'},
                        {field: 'money_after', title: '提后余额', operate:'BETWEEN'},
                        
                        {field: 'status', title: __('Status'), searchList: {"0":__('Status 0'),"1":__('Status 1'),"2":__('Status 2'),"3":__('Status 3')}, formatter: Table.api.formatter.status,custom:{'0':'warning','1':'success','2':'danger','3':'muted'}},
                        
                        {field: 'channel', title: '提现方式', searchList: {"wx":__('Channel wx'),"wxpay":__('Channel wxpay'),"ali":__('Channel ali'),"bank":__('Channel bank')}, formatter: Table.api.formatter.normal,custom:{'wx':'success','ali':'info','bank':'black'}},
						{field: 'channel_name', title: '账户名称',operate: 'LIKE'},
                        {field: 'channel_account', title: '收款账号',operate: 'LIKE'},
                        {field: 'channel_realname', title: '真实姓名', operate: 'LIKE'},
						{field: 'createtime', title: '提现时间', operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},

                        {field: 'admin_id', title: __('Admin_id'),visible: false,searchable:false},
                        {field: 'admin.username', title: '处理者账号', operate: 'LIKE',visible: false,searchable:false},
                        {field: 'admin.nickname', title: '处理者', operate: 'LIKE'},
						{field: 'status_time', title: '处理时间', operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        //{field: 'admin.avatar', title: __('Admin.avatar'), operate: 'LIKE', events: Table.api.events.image, formatter: Table.api.formatter.image},
						{field: 'remark', title: '处理备注', operate: 'LIKE',visible: false},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate,
							buttons: [{
                                    name: 'deal',
                                    title: '处理',
									text: '处理',
                                    classname: 'btn btn-xs btn-info btn-dialog',
                                    icon: 'fa fa-gavel',
                                    url: 'vppz/outcash/deal',
                                    //callback: function (data) {
                                    //    Layer.alert("接收到回传数据：" + JSON.stringify(data), {title: "回传数据"});
                                    //}
                                }]		
						}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
		deal: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
