<?php

namespace app\admin\model\vppz;

use think\Model;
use traits\model\SoftDelete;

class Service extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'vppz_service';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'stype_text',
		'logo_image_url',	// 需在此处追加属性，才会触发获取器 getLogoImageUrlAttr
		'icon_image_url'	// 需在此处追加属性，才会触发获取器 getIconImageUrlAttr
    ];
    

    protected static function init()
    {
        self::afterInsert(function ($row) {
            $pk = $row->getPk();
            $row->getQuery()->where($pk, $row[$pk])->update(['weigh' => $row[$pk]]);
        });
    }

    
    public function getStypeList()
    {
        return ['10' => __('Stype 10'), '15' => __('Stype 15'), '20' => __('Stype 20'), '30' => __('Stype 30'), '40' => __('Stype 40'), '110' => __('Stype 110'), '210' => __('Stype 210')];
    }


    public function getStypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['stype']) ? $data['stype'] : '');
        $list = $this->getStypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }


	// 获取器直接转换url
	public function getLogoImageUrlAttr($value,$data)
    {
        return \addons\vppz\library\Vpower::dourl($data['logo_image']);
    }

	public function getIconImageUrlAttr($value,$data)
    {
        return \addons\vppz\library\Vpower::dourl($data['icon_image']);
    }

}
