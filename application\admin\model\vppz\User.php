<?php

namespace app\admin\model\vppz;

use think\Model;


class User extends Model
{

    

    

    // 表名
    protected $name = 'vppz_user';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
		'avatar_url'	// 需在此处追加属性，才会触发获取器 getAvatarUrlAttr
    ];
    

    







    public function area()
    {
        return $this->belongsTo('Area', 'area_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

	// 获取器直接转换url
	public function getAvatarUrlAttr($value,$data)
    {
        return \addons\vppz\library\Vpower::dourl($data['avatar']);
    }
}
