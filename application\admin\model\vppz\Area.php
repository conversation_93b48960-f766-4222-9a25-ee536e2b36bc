<?php

namespace app\admin\model\vppz;

use think\Model;
use traits\model\SoftDelete;

class Area extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'vppz_area';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'odmode_text',
		'seller_reg_text',
        'staff_reg_text',
        'staff_team_text',
		'staff_card_text',
    ];
    

    protected static function init()
    {
        self::afterInsert(function ($row) {
            $pk = $row->getPk();
            $row->getQuery()->where($pk, $row[$pk])->update(['weigh' => $row[$pk]]);
        });
    }

    
    public function getOdmodeList()
    {
        return ['0' => __('Odmode 0'), '10' => __('Odmode 10')];
    }

    public function getSellerRegList()
    {
        return ['1' => __('Seller_reg 1'), '2' => __('Seller_reg 2'), '3' => __('Seller_reg 3')];
    }

    public function getStaffRegList()
    {
        return ['1' => __('Staff_reg 1'), '2' => __('Staff_reg 2'), '3' => __('Staff_reg 3'), '4' => __('Staff_reg 4')];
    }

    public function getStaffTeamList()
    {
        return ['1' => __('Staff_team 1'), '2' => __('Staff_team 2')];
    }

    public function getStaffCardList()
    {
        return ['0' => __('Staff_card 0'), '1' => __('Staff_card 1')];
    }

    public function getOdmodeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['odmode']) ? $data['odmode'] : '');
        $list = $this->getOdmodeList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    public function getSellerRegTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['seller_reg']) ? $data['seller_reg'] : '');
        $list = $this->getSellerRegList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    public function getStaffRegTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['staff_reg']) ? $data['staff_reg'] : '');
        $list = $this->getStaffRegList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getStaffTeamTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['staff_team']) ? $data['staff_team'] : '');
        $list = $this->getStaffTeamList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    public function getStaffCardTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['staff_card']) ? $data['staff_card'] : '');
        $list = $this->getStaffCardList();
        return isset($list[$value]) ? $list[$value] : '';
    }

}
