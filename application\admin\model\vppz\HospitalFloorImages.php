<?php

namespace app\admin\model\vppz;

use think\Model;
use traits\model\SoftDelete;

/**
 * 医院楼层图片模型
 * 
 * @property int $id 主键ID
 * @property int $hospital_id 医院ID
 * @property string $title 楼层标题
 * @property string $pic 楼层图片路径
 * @property int $status 审核状态：0=待审核，1=审核通过，2=审核拒绝
 * @property int $admin_id 审核管理员ID
 * @property int $audit_time 审核时间
 * @property string $audit_remark 审核备注
 * @property int $createtime 创建时间
 * @property int $updatetime 更新时间
 */
class HospitalFloorImages extends Model
{
    use SoftDelete;

    // 表名
    protected $name = 'vppz_hospital_floor_images';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'pic_url',
        'status_text'
    ];

    // 状态映射
    public static $statusList = [
        0 => '待审核',
        1 => '审核通过', 
        2 => '审核拒绝'
    ];

    /**
     * 获取图片完整URL
     */
    public function getPicUrlAttr($value, $data)
    {
        return \addons\vppz\library\Vpower::dourl($data['pic']);
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        return self::$statusList[$data['status']] ?? '未知状态';
    }

    /**
     * 关联医院
     */
    public function hospital()
    {
        return $this->belongsTo('Hospital', 'hospital_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

    /**
     * 关联审核管理员
     */
    public function admin()
    {
        return $this->belongsTo('app\admin\model\Admin', 'admin_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
} 