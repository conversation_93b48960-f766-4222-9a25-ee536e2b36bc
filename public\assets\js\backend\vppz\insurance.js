define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {
    // 控制器对象
    var Controller = {
        // 列表页方法
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'vppz/insurance/index' + location.search, // 数据接口
                    edit_url: 'vppz/insurance/edit', // 编辑接口，修复编辑按钮404
                    //add_url: 'vppz/insurance/add',
                    //del_url: 'vppz/insurance/del',
                    multi_url: 'vppz/insurance/multi',
                    import_url: 'vppz/insurance/import',
                    table: 'vppz_insurance_apply', // 表名
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url, // 数据接口
                pk: 'id', // 主键
                sortName: 'id', // 默认排序字段
                columns: [
                    [
                        {checkbox: true}, // 复选框
                        {field: 'id', title: 'ID'},
                        {field: 'client.name', title: '申请人', operate: 'LIKE'},
                        {field: 'client_mobile', title: '联系电话', operate: 'LIKE'},
                        {field: 'status', title: '状态', searchList: {'0': '待审核', '1': '已通过', '2': '已拒绝'}, formatter: Table.api.formatter.status},
                        {field: 'createtime', title: '申请时间', operate: 'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: '更新时间', operate: 'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        // 自定义操作列，只显示审批和编辑按钮
                        {
                            field: 'operate',
                            title: '操作',
                            table: table,
                            events: Table.api.events.operate,
                            formatter: function (value, row, index) {
                                var buttons = [];
                                // 审批按钮，仅待审核时显示
                                if (row.status == '0') {
                                    buttons.push('<a href="javascript:;" class="btn btn-xs btn-success btn-approve" data-id="' + row.id + '"><i class="fa fa-check"></i> 审批</a>');
                                }
                                // 编辑按钮，始终显示
                                buttons.push('<a href="javascript:;" class="btn btn-xs btn-primary btn-editone" data-id="' + row.id + '"><i class="fa fa-pencil"></i> 编辑</a>');
                                return buttons.join(' ');
                            }
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
            // 审批按钮事件绑定
            $(document).on('click', '.btn-approve', function () {
                var id = $(this).data('id');
                // 跳转到approve页面，带上id参数，标题为审批
                Fast.api.open('vppz/insurance/approve/ids/' + id, '审批');
            });
        },
        // 新增页方法（如有）
        add: function () {
            Controller.api.bindevent();
        },
        // 编辑页方法（如有）
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            // 表单事件绑定
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
}); 