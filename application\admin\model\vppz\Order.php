<?php

namespace app\admin\model\vppz;

use think\Model;


class Order extends Model
{

    

    

    // 表名
    protected $name = 'vppz_order';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'service_stype_text',
        'client_sex_text',
        'starttime_text',
        'endtime_text',
        'status_text',
        'status_time_text',
        'pay_time_text',
        'refund_status_text',
        'refund_time_text',
        'staff_status_text',
        'staff_time_text',

		'service_logo_image_url'	// 需在此处追加属性，才会触发获取器 getServiceLogoImageUrlAttr
    ];
    

    
    public function getServiceStypeList()
    {
        return ['10' => __('Service_stype 10'), '15' => __('Service_stype 15'), '20' => __('Service_stype 20'), '30' => __('Service_stype 30'), '40' => __('Service_stype 40'), '110' => __('Service_stype 110'), '210' => __('Service_stype 210')];
    }

    public function getClientSexList()
    {
        return ['1' => __('Client_sex 1'), '2' => __('Client_sex 2')];
    }

    public function getStatusList()
    {
        return ['10' => __('Status 10'), '20' => __('Status 20'), '30' => __('Status 30'), '40' => __('Status 40')];	//'0' => __('Status 0'), 
    }

    public function getRefundStatusList()
    {
        return ['0' => __('Refund_status 0'), '1' => __('Refund_status 1'), '2' => __('Refund_status 2'), '3' => __('Refund_status 3')];
    }

    public function getStaffStatusList()
    {
        return ['0' => __('Staff_status 0'), '10' => __('Staff_status 10')];
    }


    public function getServiceStypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['service_stype']) ? $data['service_stype'] : '');
        $list = $this->getServiceStypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getClientSexTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['client_sex']) ? $data['client_sex'] : '');
        $list = $this->getClientSexList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getStarttimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['starttime']) ? $data['starttime'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getEndtimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['endtime']) ? $data['endtime'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getStatusTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status_time']) ? $data['status_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getPayTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['pay_time']) ? $data['pay_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getRefundStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['refund_status']) ? $data['refund_status'] : '');
        $list = $this->getRefundStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getRefundTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['refund_time']) ? $data['refund_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getStaffStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['staff_status']) ? $data['staff_status'] : '');
        $list = $this->getStaffStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getStaffTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['staff_time']) ? $data['staff_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setStarttimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setEndtimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setStatusTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setPayTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setRefundTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setStaffTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


    public function area()
    {
        return $this->belongsTo('Area', 'area_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function user()
    {
        return $this->belongsTo('User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function staff()
    {
        return $this->belongsTo('Staff', 'staff_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

	

	// 获取器直接转换url
	public function getServiceLogoImageUrlAttr($value,$data)
    {
        return \addons\vppz\library\Vpower::dourl($data['service_logo_image']);
    }
}
