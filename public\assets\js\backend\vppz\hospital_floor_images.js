define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 获取URL参数中的医院ID
            var urlParams = new URLSearchParams(location.search);
            var hospitalId = urlParams.get('hospital_id') || 0;
            
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'vppz/hospital_floor_images/index' + (hospitalId ? ('?hospital_id=' + hospitalId) : ''),
                    add_url: 'vppz/hospital_floor_images/add' + (hospitalId ? ('?hospital_id=' + hospitalId) : ''),
                    audit_url: 'vppz/hospital_floor_images/audit',
                    del_url: 'vppz/hospital_floor_images/del',
                    multi_url: 'vppz/hospital_floor_images/multi',
                    table: 'vppz_hospital_floor_images',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'createtime',
                sortOrder: 'desc',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'hospital.name', title: '医院名称', operate: 'LIKE', visible: !Config.hospital_id || Config.hospital_id == 0},
                        {field: 'title', title: '楼层标题', operate: 'LIKE'},
                        {field: 'pic', title: '楼层图片', events: Table.api.events.image, formatter: Table.api.formatter.image},
                        {field: 'status', title: '审核状态', searchList: {"0":"待审核","1":"审核通过","2":"审核拒绝"}, formatter: Table.api.formatter.status},
                        {field: 'admin.nickname', title: '审核人', operate: false},
                        {field: 'audit_time', title: '审核时间', operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'audit_remark', title: '审核备注', operate: 'LIKE'},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate,
                            buttons: [{
                                name: 'audit',
                                title: '审核',
                                text: '审核',
                                classname: 'btn btn-xs btn-info btn-dialog',
                                icon: 'fa fa-check',
                                url: 'vppz/hospital_floor_images/audit',
                                callback: function (data) {
                                    Layer.alert("审核成功", {title: "提示"});
                                }
                            }]
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        audit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
}); 