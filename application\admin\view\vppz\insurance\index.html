<div class="panel panel-default panel-intro">
    <!-- 面板头部，包含标题和tab切换 -->
    <div class="panel-heading">
        {:build_heading(null,FALSE)}
        <ul class="nav nav-tabs" data-field="status">
            <li class="{: 24Think.get.status === null ? 'active' : ''}"><a href="#t-all" data-value="" data-toggle="tab">全部</a></li>
            {foreach name="statusList" item="vo" key="key"}
            <li class="{: 24Think.get.status === (string)$key ? 'active' : ''}"><a href="#t-{$key}" data-value="{$key}" data-toggle="tab">{$vo}</a></li>
            {/foreach}
        </ul>
    </div>
    <!-- 面板主体，包含工具栏和表格 -->
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <!-- 工具栏，可根据需要添加按钮 -->
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="刷新"><i class="fa fa-refresh"></i> </a>
                        <!--
                        <a href="javascript:;" class="btn btn-success btn-add" title="新增"><i class="fa fa-plus"></i> 新增</a>
                        <a href="javascript:;" class="btn btn-success btn-edit btn-disabled disabled" title="编辑"><i class="fa fa-pencil"></i> 编辑</a>
                        <a href="javascript:;" class="btn btn-danger btn-del btn-disabled disabled" title="删除"><i class="fa fa-trash"></i> 删除</a>
                        -->
                    </div>
                    <!-- 数据表格 -->
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap" width="100%">
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>