<?php

namespace app\admin\model\vppz;

use think\Model;

class AppBaseModel extends Model
{

	/*** 由于关联模型下全局查询范围会报错（可能是查询语句组装错误），所以目前没有使用该功能
	// app区分
    protected function base($query)
    {
		if(method_exists('getDeleteTimeField')){
			$field = $this->getDeleteTimeField(true);
			return $field ? $query->useSoftDelete($field)->where('app_id',1) : $query->where('app_id',1);
		}else{
			return $query->where('app_id',1);
		}
    }
	**/
    
}
