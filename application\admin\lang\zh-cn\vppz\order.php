<?php

return [
    'Service_id'        => '服务ID',
    'Service_stype'     => '服务类型',
    'Service_stype 10'  => '到院陪诊',
    'Service_stype 15'  => '接送陪诊',
    'Service_stype 20'  => '代排约号',
    'Service_stype 30'  => '送取结果',
    'Service_stype 40'  => '代跑取药',
    'Service_stype 110' => '上门服务',
	'Stype 210'			=> '自定义付费',
    'Service_name'      => '服务名',
    'Title'             => '交易标题',
    'User_id'           => 'mine的id',
    'Client_sex'        => '性别',
    'Client_sex 1'      => '男',
    'Client_sex 2'      => '女',
    'Num'               => '内部订单号',
    'Price'             => '单价',
    'Cnt'               => '数量',
    'Amount'            => '总价',
    'Fee'               => '服务费',
    'Cut'               => '优惠额度',
    'To_pay'            => '实际支付额',
    'Tax_seller'        => '平台利润',
    'Starttime'         => '服务开始时间',
    'Endtime'           => '服务结束时间',
    'Status'            => '订单状态',
    'Status 0'          => '未提交',
    'Status 10'         => '已提交未付款',
    'Status 20'         => '已付款待服务',
    'Status 30'         => '服务完成',
    'Status 40'         => '已取消',
    'Prepay_params'     => '支付前相关参数',
    'Pay_way'           => '0:直接在线支付,10:后台管理员手动充值',
    'Pay'               => '实际付款（元）',
    'Pay_params'        => '支付相关参数',
    'Pay_time'          => '付款时间',
    'Refund_status'     => '退款状态',
    'Refund_status 0'   => '未退款',
    'Refund_status 1'   => '等待退款',
    'Refund_status 2'   => '退款成功',
    'Refund_status 3'   => '退款失败',
    'Refund_money'      => '待退款金额',
    'Refund'            => '实际已退款金额',
    'Refund_time'       => '退款时间',
    'Refund_remark'     => '退款备注',
    'Refund_tag'        => '退款接口反馈信息',
    'Staff_touid'       => '指派接单者uid',
    'Staff_status'      => '接单状态',
    'Staff_status 0'    => '未接单',
    'Staff_status 10'   => '已接单',
    'Staff.sex'         => '性别',
    'Staff.sex 1'       => '男',
    'Staff.sex 2'       => '女'
];
