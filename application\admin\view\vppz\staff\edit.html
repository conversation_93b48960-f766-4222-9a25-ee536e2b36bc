<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">所属运营区:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-area_id" data-rule="required" data-source="vppz/area/index" class="form-control selectpage" name="row[area_id]" type="text" value="{$row.area_id|htmlentities}">
        </div>
    </div>
   
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">称呼:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-nickname" data-rule="required" class="form-control" name="row[nickname]" type="text" value="{$row.nickname|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">头像:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-avatar" data-rule="required" class="form-control" size="50" name="row[avatar]" type="text" value="{$row.avatar|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-avatar" class="btn btn-danger faupload" data-input-id="c-avatar" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-avatar"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-avatar" class="btn btn-primary fachoose" data-input-id="c-avatar" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-avatar"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-avatar"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">性别:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-sex" data-rule="required" class="form-control selectpicker" name="row[sex]">
                {foreach name="sexList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.sex"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">年龄:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-age" data-rule="required" class="form-control" name="row[age]" type="number" value="{$row.age|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">手机号:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-mobile" data-rule="required" class="form-control" name="row[mobile]" type="text" value="{$row.mobile|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">真实姓名:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-realname" data-rule="required" class="form-control" name="row[realname]" type="text" value="{$row.realname|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">身份证号:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-idcardnum" data-rule="required" class="form-control" name="row[idcardnum]" type="text" value="{$row.idcardnum|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">身份证照片:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-idcards_images" data-rule="required" class="form-control" size="50" name="row[idcards_images]" type="textarea" value="{$row.idcards_images|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-idcards_images" class="btn btn-danger faupload" data-input-id="c-idcards_images" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="true" data-preview-id="p-idcards_images"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-idcards_images" class="btn btn-primary fachoose" data-input-id="c-idcards_images" data-mimetype="image/*" data-multiple="true"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-idcards_images"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-idcards_images"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">其他资质照片:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-papers_images" class="form-control" size="50" name="row[papers_images]" type="textarea" value="{$row.papers_images|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-papers_images" class="btn btn-danger faupload" data-input-id="c-papers_images" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="true" data-preview-id="p-papers_images"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-papers_images" class="btn btn-primary fachoose" data-input-id="c-papers_images" data-mimetype="image/*" data-multiple="true"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-papers_images"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-papers_images"></ul>
        </div>
    </div>
   
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">上级团长UID:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-master_uid" data-rule="required" class="form-control" name="row[master_uid]" type="number" value="{$row.master_uid|htmlentities}">
        </div>
    </div>
   

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">备注:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-remark" data-rule="required" class="form-control " rows="5" name="row[remark]" cols="50">{$row.remark|htmlentities}</textarea>
        </div>
    </div>

    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
