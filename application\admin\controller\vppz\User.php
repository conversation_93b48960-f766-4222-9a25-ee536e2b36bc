<?php

namespace app\admin\controller\vppz;

use app\common\controller\Backend;

/**
 * 
 *
 * @icon fa fa-circle-o
 */
class User extends Base
{

    /**
     * User模型对象
     * @var \app\admin\model\vppz\User
     */
    protected $model = null;

	protected $searchFields = ['id','nickname','mobile'];
	protected $multiFields = ['black_switch']; 
	

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\vppz\User;

    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
			
			// TODO 使快速搜索框支持查询加密UID，但是导致一个BUG即快速搜索与精确搜索同时时，以快速搜索的OR条件为准，精确搜索条件无效，所以两个搜索不能同时使用
			$search = $this->request->request('search');
			$uidWhere=[];
			if($search && !is_numeric($search)){
				$uid = \addons\vppz\library\Vpower::pdecode($search);
				if($uid && intval($uid)>0){
					$uidWhere['user.id']=$uid;
					// $this->model->whereOr('user.id',1); // 这样写不行,必须写在下面链式操作
				}
			}
			
			$adminAreasWhere=$this->adminAreasWhere('area_id');

            $list = $this->model
                    ->with(['area'])
                    ->where($where)->where($adminAreasWhere)->whereOr($uidWhere)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                
                $row->getRelation('area')->visible(['name']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

}
