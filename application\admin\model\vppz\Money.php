<?php

namespace app\admin\model\vppz;

use think\Model;


class Money extends Model
{

    

    

    // 表名
    protected $name = 'vppz_money';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'who_text',
        'biz_text',
        'biz_type_text'
    ];
    

    
    public function getWhoList()
    {
        return ['staff' => __('Who staff'), 'plat' => __('Who plat'), 'area' => __('Who area'), 'user' => __('Who user'), 'seller' => __('Who seller')];
    }

    public function getBizList()
    {
        return ['order' => __('Biz order'), 'outcash' => __('Biz outcash')];
    }

    public function getBizTypeList()
    {
        return ['service' => __('Biz_type service'), 'master' => __('Biz_type master'), 'plat' => __('Biz_type plat'), 'area' => __('Biz_type area'), 'seller' => __('Biz_type seller')];
    }


    public function getWhoTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['who']) ? $data['who'] : '');
        $list = $this->getWhoList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getBizTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['biz']) ? $data['biz'] : '');
        $list = $this->getBizList();
        return isset($list[$value]) ? $list[$value] : '';
    }


    public function getBizTypeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['biz_type']) ? $data['biz_type'] : '');
        $list = $this->getBizTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }




    public function user()
    {
        return $this->belongsTo('User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
