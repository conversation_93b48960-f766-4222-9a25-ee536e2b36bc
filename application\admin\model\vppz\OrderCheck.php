<?php

namespace app\admin\model\vppz;

use think\Model;


class OrderCheck extends Model
{

    

    

    // 表名
    protected $name = 'vppz_order_check';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
		'images_urls'	// 需在此处追加属性，才会触发获取器 getImagesUrlsAttr
    ];
    

    







    public function staff()
    {
        return $this->belongsTo('Staff', 'staff_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function order()
    {
        return $this->belongsTo('Order', 'order_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function area()
    {
        return $this->belongsTo('Area', 'area_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


	// 获取器直接转换images
	public function getImagesAttr($value)
    {
        return unserialize($value);
    }

	// 获取器直接转换images匹配后台显示格式供后台使用
	public function getImagesUrlsAttr($value,$data)
    {
		$urls=[];
		if($data['images']){
			$imgs = unserialize($data['images']);
			for($i=0;$i<count($imgs);$i++){
				$urls[]=$imgs[$i]['url'];
			}
		}
		
        return $urls;
    }

	// 获取器直接转换postion
	public function getPostionAttr($value)
    {
        return unserialize($value);
    }
}
