<style>
	.text-label{padding-top:0 !important;}
</style>

<form id="deal-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
	
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2 text-label">提现者角色:</label>
        <div class="col-xs-12 col-sm-8">
			{$row.biz_text|htmlentities}：{$row.nickname|htmlentities}（电话：{$row.mobile|htmlentities}）
        </div>
    </div>

	<!--
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2 text-label">提现用户:</label>
        <div class="col-xs-12 col-sm-8">
            {$row.nickname|htmlentities}
        </div>
    </div>
	
   
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2 text-label">联系电话:</label>
        <div class="col-xs-12 col-sm-8">
            {$row.mobile|htmlentities}
        </div>
    </div>
	-->
   
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2 text-label">{:__('Channel')}:</label>
        <div class="col-xs-12 col-sm-8">
            {$row.channel_text|htmlentities}
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2 text-label">实际提现金额</label>
        <div class="col-xs-12 col-sm-8">
            <label>{$row.cash|htmlentities}元</label>
        </div>
    </div>


	<!--
	<div class="form-group">
        <label class="control-label col-xs-12 col-sm-2 text-label">账户名称:</label>
        <div class="col-xs-12 col-sm-8">
			{$row.channel_name|htmlentities}
        </div>
    </div>
	-->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2 text-label">收款账户:</label>
        <div class="col-xs-12 col-sm-8">
			<label>{$row.channel_name|htmlentities}，账号：{$row.channel_account|htmlentities}</label>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2 text-label">真实姓名:</label>
        <div class="col-xs-12 col-sm-8">
			<label>{$row.channel_realname|htmlentities}</label>
        </div>
    </div>
	
	<!--
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
	-->
	
	{if $row.status==0}
		<div class="form-group">
			<label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
			<div class="col-xs-12 col-sm-8">
				
				<div class="radio">
					<label for="row[status]-1"><input id="row[status]-1" name="row[status]" type="radio" value="1" data-rule="checked" /> 已完成</label> 
					<label for="row[status]-2"><input id="row[status]-2" name="row[status]" type="radio" value="2" data-rule="checked" /> 拒绝（不退账）</label> 
					<label for="row[status]-3"><input id="row[status]-3" name="row[status]" type="radio" value="3" data-rule="checked" /> 失败（退账）</label> 
				</div>

			</div>
		</div>

		<div class="form-group" data-favisible="status=2,3">
			<label class="control-label col-xs-12 col-sm-2">{:__('Fedback')}:</label>
			<div class="col-xs-12 col-sm-8">
				<textarea id="c-fedback" class="form-control " rows="5" name="row[fedback]" cols="50" data-rule="required">{$row.fedback|htmlentities}</textarea>
			</div>
		</div>
	{else}
		<div class="form-group">
			<label class="control-label col-xs-12 col-sm-2 text-label">{:__('Status')}:</label>
			<div class="col-xs-12 col-sm-8">
				{$row.status_text|htmlentities}
			</div>
		</div>
		{if $row.status>1}
		<div class="form-group">
			<label class="control-label col-xs-12 col-sm-2 text-label">{:__('Fedback')}:</label>
			<div class="col-xs-12 col-sm-8">
				{$row.fedback|htmlentities}
			</div>
		</div>
		{/if}
		<div class="form-group">
			<label class="control-label col-xs-12 col-sm-2 text-label">处理者:</label>
			<div class="col-xs-12 col-sm-8">
				{$row.admin.nickname|htmlentities}
			</div>
		</div>
		<div class="form-group">
			<label class="control-label col-xs-12 col-sm-2 text-label">处理时间:</label>
			<div class="col-xs-12 col-sm-8">
				{$row.status_time|datetime}
			</div>
		</div>
	{/if}

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">备注:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-remark" class="form-control " rows="5" name="row[remark]" cols="50">{$row.remark|htmlentities}</textarea>
        </div>
    </div>


    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
