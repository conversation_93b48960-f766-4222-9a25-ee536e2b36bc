<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">


    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">所属运营区:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-area_id" data-rule="required" data-source="vppz/area/index" class="form-control selectpage" name="row[area_id]" type="text" value="{$row.area_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">医院名称:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text" value="{$row.name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">医院形象照片:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-avatar" data-rule="required" class="form-control" size="50" name="row[avatar]" type="text" value="{$row.avatar|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-avatar" class="btn btn-danger faupload" data-input-id="c-avatar" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-avatar"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-avatar" class="btn btn-primary fachoose" data-input-id="c-avatar" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-avatar"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-avatar"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">医院等级:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-rank" data-rule="required" class="form-control" name="row[rank]" type="text" value="{$row.rank|htmlentities}">
			<h6 class="text-muted">例：三甲</h6>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">医院类型:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-label" data-rule="required" class="form-control" name="row[label]" type="text" value="{$row.label|htmlentities}">
			<h6 class="text-muted">例：综合病院</h6>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">医院简介:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-intro" data-rule="required" class="form-control " rows="5" name="row[intro]" cols="50">{$row.intro|htmlentities}</textarea>
        </div>
    </div>

	<div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">所在区域:</label>
        <div class="col-xs-12 col-sm-8">
            <div class='control-relative'><input id="c-city" data-rule="required" class="form-control" data-toggle="city-picker" name="row[city]" type="text" value="{$row.province|htmlentities}/{$row.city|htmlentities}/{$row.district|htmlentities}"></div>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">医院位置:</label>
        <div class="col-xs-12 col-sm-8">
			<div class="input-group">
				<div class="input-group-addon">经度</div>
				<input id="c-lng" data-rule="required" class="form-control" name="row[lng]" type="number" value="{$row.lng|htmlentities}">
				<div class="input-group-addon">纬度</div>
                <input id="c-lat" data-rule="required" class="form-control"  name="row[lat]" type="number" value="{$row.lat|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" class="btn btn-primary" data-input-id="c-address" data-toggle="addresspicker" data-lat-id="c-lat" data-lng-id="c-lng"><i class="fa fa-map-marker"></i> 选择位置</button></span>
                </div>
            </div>
        </div>
    </div>

	<div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">详细地址:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-address" data-rule="required" class="form-control" name="row[address]" type="text" value="{$row.address|htmlentities}">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">是否启用:</label>
        <div class="col-xs-12 col-sm-8">
            <input  id="c-use_switch" name="row[use_switch]" type="hidden" value="{$row.use_switch|htmlentities}">
			<a href="javascript:;" data-toggle="switcher" class="btn-switcher" data-input-id="c-use_switch"  data-yes="1" data-no="0">
			<i class="fa fa-toggle-on text-success {eq name="$row.use_switch" value="0"}fa-flip-horizontal text-gray{/eq} fa-2x"></i>
			</a>
        </div>
    </div>

    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
