<div class="panel panel-default panel-intro">
    {:build_heading()}
	
	<div class="alert alert-info-light" style="margin-bottom:10px;">
		<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="vppz/area_admin/add?area_id={$Request.param.ids}">

			<div class="form-group" style="margin:15px;">
				<label class="control-label col-xs-12 col-sm-2">添加管理员:</label>
				<div class="col-xs-12 col-sm-8">
					<input id="c-name" data-rule="required" class="form-control" name="username" type="text" placeholder="要添加的管理员用户名...">
				</div>
				<div>
					<button type="submit" class="btn btn-primary btn-embossed">{:__('OK')}</button>
				</div>
			</div>
			<!--
			<div class="form-group">
				<label class="control-label col-xs-12 col-sm-2">{:__('Area_id')}:</label>
				<div class="col-xs-12 col-sm-8">
					<input id="c-area_id" data-rule="required" data-source="area/index" class="form-control selectpage" name="row[area_id]" type="text" value="">
				</div>
			</div>
			<div class="form-group">
				<label class="control-label col-xs-12 col-sm-2"></label>
				<div class="col-xs-12 col-sm-8">
					<button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
					<button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
				</div>
			</div>
			-->
		</form>
	</div>

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}" ><i class="fa fa-refresh"></i> </a>
                        <!--<a href="javascript:;" class="btn btn-success btn-add {:$auth->check('vppz/area_admin/add')?'':'hide'}" title="{:__('Add')}" ><i class="fa fa-plus"></i> {:__('Add')}</a>
                        <a href="javascript:;" class="btn btn-success btn-edit btn-disabled disabled {:$auth->check('vppz/area_admin/edit')?'':'hide'}" title="{:__('Edit')}" ><i class="fa fa-pencil"></i> {:__('Edit')}</a>
						-->
                        <a href="javascript:;" class="btn btn-danger btn-del btn-disabled disabled {:$auth->check('vppz/area_admin/del')?'':'hide'}" title="{:__('Delete')}" ><i class="fa fa-trash"></i> {:__('Delete')}</a>
                        
						<!--
                        <div class="dropdown btn-group {:$auth->check('vppz/area_admin/multi')?'':'hide'}">
                            <a class="btn btn-primary btn-more dropdown-toggle btn-disabled disabled" data-toggle="dropdown"><i class="fa fa-cog"></i> {:__('More')}</a>
                            <ul class="dropdown-menu text-left" role="menu">
                                <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:;" data-params="status=normal"><i class="fa fa-eye"></i> {:__('Set to normal')}</a></li>
                                <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:;" data-params="status=hidden"><i class="fa fa-eye-slash"></i> {:__('Set to hidden')}</a></li>
                            </ul>
                        </div>
						-->

                        
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           data-operate-edit="{:$auth->check('vppz/area_admin/edit')}"
                           data-operate-del="{:$auth->check('vppz/area_admin/del')}"
                           width="100%">
                    </table>
                </div>
            </div>

        </div>
    </div>
</div>
