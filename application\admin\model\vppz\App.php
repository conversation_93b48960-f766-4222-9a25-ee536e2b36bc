<?php

namespace app\admin\model\vppz;

use think\Model;

class App extends Model
{
    // 表名
    protected $name = 'vppz_app';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';

    // 追加属性
    protected $append = [
		'logo_url',
		'logob_url',
		'share_image_url',
		'share_poster_url',
		'mpappqrcode_url'
	];

	// 获取器直接转换url
	public function getLogoUrlAttr($value,$data)
    {
        return \addons\vppz\library\Vpower::dourl($data['logo']);
    }

	public function getLogobUrlAttr($value,$data)
    {
        return \addons\vppz\library\Vpower::dourl($data['logob']);
    }

	public function getShareImageUrlAttr($value,$data)
    {
        return \addons\vppz\library\Vpower::dourl($data['share_image']);
    }

	public function getSharePosterUrlAttr($value,$data)
    {
        return \addons\vppz\library\Vpower::dourl($data['share_poster']);
    }

	public function getMpappqrcodeUrlAttr($value,$data)
    {
        return \addons\vppz\library\Vpower::dourl($data['mpappqrcode']);
    }
}
