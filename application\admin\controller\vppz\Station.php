<?php
namespace app\admin\controller\vppz;

use app\admin\model\vppz\Station as StationModel;

/**
 * 服务站点管理
 */
class Station extends Base
{
    protected $model = null;

    // 支持快速搜索的字段
    protected $searchFields = 'name,address,contact,phone';

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new StationModel();
    }

    /**
     * 站点列表
     */
    public function index()
    {
        if ($this->request->isAjax()) {
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $list = $this->model->where($where)->order($sort, $order)->paginate($limit);
            $result = array("total" => $list->total(), "rows" => $list->items());
            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 新增站点
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $this->model->save($params);
                $this->success();
            }
            $this->error();
        }
        return $this->view->fetch();
    }

    /**
     * 编辑站点
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $row->save($params);
                $this->success();
            }
            $this->error();
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 删除站点
     */
    public function del($ids = null)
    {
        $ids = $ids ? $ids : $this->request->post("ids");
        if ($ids) {
            $this->model->destroy($ids);
            $this->success();
        }
        $this->error();
    }
} 