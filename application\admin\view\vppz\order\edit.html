<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('App_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-app_id" data-rule="required" data-source="app/index" class="form-control selectpage" name="row[app_id]" type="text" value="{$row.app_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Area_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-area_id" data-rule="required" data-source="area/index" class="form-control selectpage" name="row[area_id]" type="text" value="{$row.area_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Area_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-area_name" data-rule="required" class="form-control" name="row[area_name]" type="text" value="{$row.area_name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Service_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-service_id" data-rule="required" data-source="service/index" class="form-control selectpage" name="row[service_id]" type="text" value="{$row.service_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Service_stype')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-service_stype" data-rule="required" class="form-control selectpicker" name="row[service_stype]">
                {foreach name="serviceStypeList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.service_stype"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Service_code')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-service_code" data-rule="required" class="form-control" name="row[service_code]" type="text" value="{$row.service_code|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Service_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-service_name" data-rule="required" class="form-control" name="row[service_name]" type="text" value="{$row.service_name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Service_logo_image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-service_logo_image" data-rule="required" class="form-control" size="50" name="row[service_logo_image]" type="text" value="{$row.service_logo_image|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-service_logo_image" class="btn btn-danger faupload" data-input-id="c-service_logo_image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-service_logo_image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-service_logo_image" class="btn btn-primary fachoose" data-input-id="c-service_logo_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-service_logo_image"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-service_logo_image"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Hospital_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-hospital_id" data-rule="required" data-source="hospital/index" class="form-control selectpage" name="row[hospital_id]" type="text" value="{$row.hospital_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Hospital_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-hospital_name" data-rule="required" class="form-control" name="row[hospital_name]" type="text" value="{$row.hospital_name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Title')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-title" data-rule="required" class="form-control" name="row[title]" type="text" value="{$row.title|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[user_id]" type="text" value="{$row.user_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Openid')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-openid" data-rule="required" class="form-control" name="row[openid]" type="text" value="{$row.openid|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Client_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-client_id" data-rule="required" data-source="client/index" class="form-control selectpage" name="row[client_id]" type="text" value="{$row.client_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Client_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-client_name" data-rule="required" class="form-control" name="row[client_name]" type="text" value="{$row.client_name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Client_sex')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-client_sex" data-rule="required" class="form-control selectpicker" name="row[client_sex]">
                {foreach name="clientSexList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.client_sex"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Client_age')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-client_age" data-rule="required" class="form-control" name="row[client_age]" type="number" value="{$row.client_age|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Client_mobile')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-client_mobile" data-rule="required" class="form-control" name="row[client_mobile]" type="text" value="{$row.client_mobile|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Client_idcard')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-client_idcard" data-rule="required" class="form-control" name="row[client_idcard]" type="text" value="{$row.client_idcard|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Address')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-address" data-rule="required" class="form-control " rows="5" name="row[address]" cols="50">{$row.address|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Tel')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-tel" data-rule="required" class="form-control" name="row[tel]" type="text" value="{$row.tel|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Demand')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-demand" data-rule="required" class="form-control " rows="5" name="row[demand]" cols="50">{$row.demand|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Num')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-num" data-rule="required" class="form-control" name="row[num]" type="text" value="{$row.num|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-price" data-rule="required" class="form-control" step="0.01" name="row[price]" type="number" value="{$row.price|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Cnt')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-cnt" data-rule="required" class="form-control" name="row[cnt]" type="number" value="{$row.cnt|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Amount')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-amount" data-rule="required" class="form-control" step="0.01" name="row[amount]" type="number" value="{$row.amount|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Fee')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-fee" data-rule="required" class="form-control" step="0.01" name="row[fee]" type="number" value="{$row.fee|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Cut')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-cut" data-rule="required" class="form-control" step="0.01" name="row[cut]" type="number" value="{$row.cut|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('To_pay')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-to_pay" data-rule="required" class="form-control" step="0.01" name="row[to_pay]" type="number" value="{$row.to_pay|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Profit')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-profit" data-rule="required" class="form-control" name="row[profit]" type="number" value="{$row.profit|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Profit_fee')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-profit_fee" data-rule="required" class="form-control" step="0.01" name="row[profit_fee]" type="number" value="{$row.profit_fee|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Tax_plat')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-tax_plat" data-rule="required" class="form-control" name="row[tax_plat]" type="number" value="{$row.tax_plat|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Tax_plat_fee')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-tax_plat_fee" data-rule="required" class="form-control" step="0.01" name="row[tax_plat_fee]" type="number" value="{$row.tax_plat_fee|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Tax_master')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-tax_master" data-rule="required" class="form-control" name="row[tax_master]" type="number" value="{$row.tax_master|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Tax_master_fee')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-tax_master_fee" data-rule="required" class="form-control" step="0.01" name="row[tax_master_fee]" type="number" value="{$row.tax_master_fee|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Tax_seller')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-tax_seller" data-rule="required" class="form-control" name="row[tax_seller]" type="number" value="{$row.tax_seller|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Tax_seller_fee')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-tax_seller_fee" data-rule="required" class="form-control" step="0.01" name="row[tax_seller_fee]" type="number" value="{$row.tax_seller_fee|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Tax_area_fee')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-tax_area_fee" data-rule="required" class="form-control" step="0.01" name="row[tax_area_fee]" type="number" value="{$row.tax_area_fee|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Starttime')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-starttime" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[starttime]" type="text" value="{:$row.starttime?datetime($row.starttime):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Endtime')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-endtime" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[endtime]" type="text" value="{:$row.endtime?datetime($row.endtime):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Remark')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-remark" data-rule="required" class="form-control " rows="5" name="row[remark]" cols="50">{$row.remark|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-status_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[status_time]" type="text" value="{:$row.status_time?datetime($row.status_time):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Prepay_params')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-prepay_params" class="form-control " rows="5" name="row[prepay_params]" cols="50">{$row.prepay_params|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Pay_way')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-pay_way" data-rule="required" class="form-control" name="row[pay_way]" type="number" value="{$row.pay_way|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Pay')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-pay" class="form-control" step="0.01" name="row[pay]" type="number" value="{$row.pay|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Pay_params')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-pay_params" class="form-control " rows="5" name="row[pay_params]" cols="50">{$row.pay_params|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Pay_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-pay_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[pay_time]" type="text" value="{:$row.pay_time?datetime($row.pay_time):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Pay_callback')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-pay_callback" data-rule="required" class="form-control" name="row[pay_callback]" type="text" value="{$row.pay_callback|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Refund_status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="refundStatusList" item="vo"}
            <label for="row[refund_status]-{$key}"><input id="row[refund_status]-{$key}" name="row[refund_status]" type="radio" value="{$key}" {in name="key" value="$row.refund_status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Refund_money')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-refund_money" class="form-control" step="0.01" name="row[refund_money]" type="number" value="{$row.refund_money|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Refund')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-refund" class="form-control" step="0.01" name="row[refund]" type="number" value="{$row.refund|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Refund_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-refund_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[refund_time]" type="text" value="{:$row.refund_time?datetime($row.refund_time):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Refund_remark')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-refund_remark" class="form-control " rows="5" name="row[refund_remark]" cols="50">{$row.refund_remark|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Refund_tag')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-refund_tag" class="form-control" data-role="tagsinput" name="row[refund_tag]" type="text" value="{$row.refund_tag|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Staff_toid')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-staff_toid" data-rule="required" class="form-control" name="row[staff_toid]" type="number" value="{$row.staff_toid|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Staff_touid')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-staff_touid" data-rule="required" class="form-control" name="row[staff_touid]" type="number" value="{$row.staff_touid|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Staff_status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="staffStatusList" item="vo"}
            <label for="row[staff_status]-{$key}"><input id="row[staff_status]-{$key}" name="row[staff_status]" type="radio" value="{$key}" {in name="key" value="$row.staff_status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Staff_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-staff_id" data-rule="required" data-source="staff/index" class="form-control selectpage" name="row[staff_id]" type="text" value="{$row.staff_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Staff_uid')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-staff_uid" data-rule="required" class="form-control" name="row[staff_uid]" type="number" value="{$row.staff_uid|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Staff_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-staff_time" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[staff_time]" type="text" value="{:$row.staff_time?datetime($row.staff_time):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Staff_remark')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-staff_remark" data-rule="required" class="form-control " rows="5" name="row[staff_remark]" cols="50">{$row.staff_remark|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
