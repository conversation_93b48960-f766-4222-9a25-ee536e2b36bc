<?php
namespace app\api\controller\vppz;


use think\Cache;


use \app\admin\model\vppz\Hospital as HospitalModel;
use \app\admin\model\vppz\Service as ServiceModel;
use \app\admin\model\vppz\HospitalService as HospitalServiceModel;

use \addons\vppz\library\Vpower;

class Hospital extends App
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];
    /** @var \app\admin\model\vppz\Hospital */
    protected $HospitalModel;
    /** @var \app\admin\model\vppz\Service */
    protected $ServiceModel;
    /** @var \app\admin\model\vppz\HospitalService */
    protected $HospitalServiceModel;

    public function _initialize(){
        parent::_initialize();
		

		$this->HospitalModel = new HospitalModel;
		$this->ServiceModel = new ServiceModel;
		$this->HospitalServiceModel = new HospitalServiceModel;
    }


    /**
     * 首页
     */
    public function index(){
		$area=$this->_area;


		$hid = input('hid');

		// 获取医院
		$hospital = $this->HospitalModel->where(['app_id'=>$this->app_id])->find($hid);
		//$hospital = pdo_fetch('SELECT * FROM ' .tablename('vp_pz_hospital') . ' where  uniacid = :uniacid AND city_id=:city_id AND id=:id AND status=1 AND is_del=0 ', array(':uniacid' => $_W['uniacid'],':city_id'=>$city['id'],':id'=>$hid));
		if(empty($hospital)){
			$this->error('该医院尚未开通');
		}
		if($hospital['use_switch']!=1){
			$this->error('该医院尚未开通相关服务');
		}

		//$hospital['avatar']=VP_IMAGE_URL($hospital['avatar']);
		//$hospital['service']=iunserializer($hospital['service']);
		$hss = $this->HospitalServiceModel->field('service_id,price,use_switch')->where(['app_id'=>$this->app_id,'area_id'=>$area['id'],'hospital_id'=>$hospital['id']])->select();

		// 获取服务
		//$services = pdo_fetchall('SELECT id,code,name,logo,intro,price FROM ' .tablename('vp_pz_service') . ' where  uniacid = :uniacid AND status=1 AND is_del=0 order by sort desc ', array(':uniacid' => $_W['uniacid']));
		$services = $this->ServiceModel->field('id,code,stype,name,logo_image,icon_image,intro,price,priceo')->where(['app_id'=>$this->app_id,'use_switch'=>1])->order('weigh', 'desc')->select();

		// 根据医院设置，覆盖服务
		for($i=0;$i<count($services);$i++){
			for($j=0;$j<count($hss);$j++){
				if($services[$i]['id']==$hss[$j]['service_id']){
					//$_service=$hospital['service'][$services[$i]['id']];
					$services[$i]['use_switch']=$hss[$j]['use_switch'];
					$services[$i]['price']=$hss[$j]['price'];
					//$services[$i]['logo']=VP_IMAGE_URL($services[$i]['logo']);
				}
			}
		}
		
		$this->success('',array(
			'now'=>time(),
			'hospital'=>$hospital,
			'services'=>$services
		));
    }

    /**
     * 获取医院楼层引导图接口
     * 前端请求方式：GET
     * 请求参数：
     *   hid 医院ID
     * 返回数据格式：
     *   {
     *     "code": 1,
     *     "msg": "success",
     *     "data": [
     *         {"title": "楼层1", "image": "图片URL"},
     *         ...
     *     ]
     *   }
     * 说明：楼层信息保存在医院楼层图片表中，只返回审核通过的图片
     */
    public function floorGuide()
    {
        // 获取医院ID参数
        $hid = input('hid/d', 0);
        if (empty($hid)) {
            // 返回错误，缺少医院ID
            $this->error('缺少医院ID参数');
        }

        // 查询医院信息
        $hospital = $this->HospitalModel->where(['app_id' => $this->app_id])->find($hid);
        if (empty($hospital)) {
            $this->error('医院不存在');
        }

        // 从楼层图片表中获取审核通过的楼层信息
        $floorImagesModel = new \app\admin\model\vppz\HospitalFloorImages;
        $floorImages = $floorImagesModel
            ->where(['hospital_id' => $hid, 'status' => 1]) // 只获取审核通过的
            ->order('createtime', 'asc')
            ->select();

        // 转换为前端需要的格式
        $floorGuideList = [];
        foreach($floorImages as $image){
            $floorGuideList[] = [
                'id' => $image['id'],
                'title' => $image['title'],
                'image' => $this->getFullImageUrl($image['pic'])
            ];
        }

        // 返回数据
        $this->success('success', $floorGuideList);
    }

    /**
     * 获取图片完整URL
     * @param string $imagePath 图片相对路径
     * @return string 完整URL
     */
    protected function getFullImageUrl($imagePath)
    {
        // 判断图片路径是否已是完整URL
        if (empty($imagePath)) {
            return '';
        }
        if (strpos($imagePath, 'http://') === 0 || strpos($imagePath, 'https://') === 0) {
            return $imagePath;
        }
        // 假设图片上传目录为 /uploads/
        $domain = request()->domain();
        $imagePath = ltrim($imagePath, '/');
        return $domain . '/' . $imagePath;
    }

    /**
     * 更新医院楼层引导图接口
     * 前端请求方式：POST
     * 请求参数：
     *   hid 医院ID
     *   id 楼层图片ID
     *   title 楼层标题
     *   image 楼层图片URL
     * 返回数据格式：
     *   {"code":1, "msg":"success"}
     */
    public function updateFloorGuide()
    {
        // 获取参数
        $hid = input('hid/d', 0);
        $id = input('id/d', 0);
        $title = input('title', '', 'trim');
        $image = input('image', '', 'trim');
        if (empty($hid) || empty($id) || empty($title) || empty($image)) {
            $this->error('参数不完整');
        }
        // 查询医院信息
        $hospital = (new \app\admin\model\vppz\Hospital)->where(['app_id' => $this->app_id])->find($hid);
        if (empty($hospital)) {
            $this->error('医院不存在');
        }
        // 查询楼层图片记录
        $floorImagesModel = new \app\admin\model\vppz\HospitalFloorImages;
        $floor = $floorImagesModel->where(['id' => $id, 'hospital_id' => $hid])->find();
        if (empty($floor)) {
            $this->error('楼层图片不存在');
        }
        // 更新数据
        $floor->title = $title;
        $floor->pic = $image;
        $floor->status = 0; // 更新后需重新审核
        $floor->save();
        $this->success('更新成功');
    }

    /**
     * 添加医院楼层引导图接口
     * 前端请求方式：POST
     * 请求参数：
     *   hid 医院ID
     *   title 楼层标题
     *   image 楼层图片URL
     * 返回数据格式：
     *   {"code":1, "msg":"添加成功，请等待审核"}
     */
    public function addFloorGuide()
    {
        // 获取参数
        $hid = input('hid/d', 0);
        $title = input('title', '', 'trim');
        $image = input('image', '', 'trim');
        if (empty($hid) || empty($title) || empty($image)) {
            $this->error('参数不完整');
        }
        // 查询医院信息
        $hospital = (new \app\admin\model\vppz\Hospital)->where(['app_id' => $this->app_id])->find($hid);
        if (empty($hospital)) {
            $this->error('医院不存在');
        }
        // 新增楼层图片
        $floorImagesModel = new \app\admin\model\vppz\HospitalFloorImages;
        $floorImagesModel->save([
            'hospital_id' => $hid,
            'title' => $title,
            'pic' => $image,
            'status' => 0, // 待审核
            'createtime' => time(),
            'updatetime' => time()
        ]);
        $this->success('添加成功，请等待审核');
    }

}