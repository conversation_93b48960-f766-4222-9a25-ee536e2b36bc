<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('App_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-app_id" data-rule="required" data-source="app/index" class="form-control selectpage" name="row[app_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Area_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-area_id" data-rule="required" data-source="area/index" class="form-control selectpage" name="row[area_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Nickname')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-nickname" data-rule="required" class="form-control" name="row[nickname]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Avatar')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-avatar" data-rule="required" class="form-control" size="50" name="row[avatar]" type="text">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-avatar" class="btn btn-danger faupload" data-input-id="c-avatar" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-avatar"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-avatar" class="btn btn-primary fachoose" data-input-id="c-avatar" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-avatar"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-avatar"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Mobile')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-mobile" data-rule="required" class="form-control" name="row[mobile]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Openid')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-openid" data-rule="required" class="form-control" name="row[openid]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Province')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-province" data-rule="required" class="form-control" name="row[province]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('City')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class='control-relative'><input id="c-city" data-rule="required" class="form-control" data-toggle="city-picker" name="row[city]" type="text"></div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('District')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-district" data-rule="required" class="form-control" name="row[district]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Orders')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-orders" data-rule="required" class="form-control" name="row[orders]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Orders_cancel')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-orders_cancel" data-rule="required" class="form-control" name="row[orders_cancel]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Expends')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-expends" data-rule="required" class="form-control" step="0.01" name="row[expends]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Admin_switch')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-admin_switch" data-rule="required" class="form-control" name="row[admin_switch]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Seller_switch')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-seller_switch" data-rule="required" class="form-control" name="row[seller_switch]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Black_switch')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-black_switch" data-rule="required" class="form-control" name="row[black_switch]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Remark')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-remark" data-rule="required" class="form-control " rows="5" name="row[remark]" cols="50"></textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Invites')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-invites" data-rule="required" class="form-control" name="row[invites]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Inviter_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-inviter_id" data-rule="required" data-source="inviter/index" class="form-control selectpage" name="row[inviter_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Sells')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-sells" data-rule="required" class="form-control" name="row[sells]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Seller_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-seller_id" data-rule="required" data-source="seller/index" class="form-control selectpage" name="row[seller_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Sell_money')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-sell_money" data-rule="required" class="form-control" step="0.01" name="row[sell_money]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Sell_income')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-sell_income" data-rule="required" class="form-control" step="0.01" name="row[sell_income]" type="number">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Sell_outcash')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-sell_outcash" data-rule="required" class="form-control" step="0.01" name="row[sell_outcash]" type="number">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
