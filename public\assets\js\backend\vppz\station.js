define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {
    // 控制器对象，封装页面相关方法
    var Controller = {
        // 列表页初始化方法
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'vppz/station/index' + location.search, // 列表数据接口
                    add_url: 'vppz/station/add', // 新增页面接口
                    edit_url: 'vppz/station/edit', // 编辑页面接口
                    del_url: 'vppz/station/del', // 删除接口
                    multi_url: 'vppz/station/multi', // 批量操作接口
                    table: 'vppz_station', // 表名
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url, // 数据接口
                pk: 'id', // 主键
                sortName: 'id', // 默认排序字段
                columns: [
                    [
                        {checkbox: true}, // 多选框
                        {field: 'id', title: 'ID'},
                        {field: 'name', title: '站点名称', operate: 'LIKE'},
                        {field: 'address', title: '地址', operate: 'LIKE'},
                        {field: 'contact', title: '联系人', operate: 'LIKE'},
                        {field: 'phone', title: '联系电话', operate: 'LIKE'},
                        {field: 'createtime', title: '创建时间', operate: 'RANGE', addclass: 'datetimerange', formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: '更新时间', operate: 'RANGE', addclass: 'datetimerange', formatter: Table.api.formatter.datetime, visible: false},
                        // 操作列，自动生成编辑、删除按钮
                        {field: 'operate', title: '操作', table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件，支持弹窗编辑和删除
            Table.api.bindevent(table);

            // 只依赖Table.api.init的add_url配置，不再手动绑定新增按钮事件，避免弹出两个弹窗
        },
        // 新增页初始化方法
        add: function () {
            // 绑定表单事件，支持表单验证和提交
            Form.api.bindevent($("form[role=form]"));
        },
        // 编辑页初始化方法
        edit: function () {
            // 绑定表单事件，支持表单验证和提交
            Form.api.bindevent($("form[role=form]"));
        }
    };
    // 返回控制器对象，供 requirejs 调用
    return Controller;
}); 