<?php
namespace app\admin\model\vppz;

use think\Model;

/**
 * 长护险管理模型
 */
class Insurance extends Model
{
    // 这里可以根据需要添加自动时间戳、表名等配置
    // protected $autoWriteTimestamp = true;
    protected $table = 'fa_vppz_insurance_apply';

    /**
     * 获取申请状态列表
     * @return array
     */
    public function getStatusList()
    {
        // 返回长护险申请的所有状态
        return [
            '0' => '待审核',
            '1' => '已通过',
            '2' => '已拒绝',
        ];
    }

    /**
     * 关联客户信息
     * @return \think\model\relation\BelongsTo
     */
    public function client()
    {
        // 关联vppz_client表，外键client_id，对应主键id
        return $this->belongsTo('Client', 'client_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
