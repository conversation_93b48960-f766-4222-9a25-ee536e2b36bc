<?php

namespace app\admin\controller\vppz;

/**
 * 医院楼层图片管理
 *
 * @icon fa fa-image
 */
class HospitalFloorImages extends Base
{

    /**
     * HospitalFloorImages模型对象
     * @var \app\admin\model\vppz\HospitalFloorImages
     */
    protected $model = null;
	
	protected $searchFields = ['id','title','hospital.name'];
	protected $multiFields = ['status']; 

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\vppz\HospitalFloorImages;
		
		// 给数据附加当前appid，appid由Base负责解析
		$this->model::event('before_insert', function ($row){
			$row->app_id=$this->app_id;
		});
		
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }

            // 获取分页、排序参数
            $sort = $this->request->get("sort", "createtime");
            $order = $this->request->get("order", "desc");
            $offset = $this->request->get("offset", 0);
            $limit = $this->request->get("limit", 10);

            // 只按医院ID筛选
            $hospitalId = $this->request->get('hospital_id/d', 0);
            if ($hospitalId == 0) {
                $hospitalId = $this->request->param('hospital_id/d', 0);
            }
            $where = [];
            if ($hospitalId > 0) {
                $where['hospital_id'] = $hospitalId;
            }

            $list = $this->model
                ->with(['hospital', 'admin'])
                ->where($where)
                ->order($sort, $order)
                ->limit($offset, $limit)
                ->select();

            $total = $this->model->where($where)->count();

            foreach ($list as $row) {
                if ($row->getRelation('hospital')) {
                    $row->getRelation('hospital')->visible(['name']);
                }
                if ($row->getRelation('admin')) {
                    $row->getRelation('admin')->visible(['nickname']);
                }
            }

            $result = array("total" => $total, "rows" => $list);
            return json($result);
        }
        
        // 传递医院ID到视图
        $hospitalId = $this->request->get('hospital_id/d', 0);
        $this->view->assign('hospital_id', $hospitalId);
        
        // 如果有医院ID，获取医院信息
        $hospital = null;
        if ($hospitalId > 0) {
            $hospital = \app\admin\model\vppz\Hospital::find($hospitalId);
        }
        $this->view->assign('hospital', $hospital);
        
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                
                // 设置默认状态为待审核
                $params['status'] = 0;
                
                $result = $this->model->allowField(true)->save($params);
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            } else {
                $this->error(__('Parameter %s can not be empty', ''));
            }
        }
        
        // 传递医院ID到视图
        $hospitalId = $this->request->get('hospital_id/d', 0);
        $hospital = null;
        if ($hospitalId > 0) {
            $hospital = \app\admin\model\vppz\Hospital::find($hospitalId);
        }
        $this->view->assign('hospital', $hospital);
        
        return $this->view->fetch();
    }
    
    /**
     * 审核
     */
    public function audit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params['admin_id'] = $this->auth->id;
                $params['audit_time'] = time();
                
                $result = $row->allowField(true)->save($params);
                if ($result !== false) {
                    $this->success('审核成功');
                } else {
                    $this->error(__('Parameter %s can not be empty', ''));
                }
            } else {
                $this->error(__('Parameter %s can not be empty', ''));
            }
        }
        
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }
} 